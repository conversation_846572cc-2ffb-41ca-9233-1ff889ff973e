package dev.felnull.imp.data;

import dev.felnull.imp.block.IMPBlocks;
import dev.felnull.imp.item.IMPItemTags;
import dev.felnull.imp.item.IMPItems;
import dev.felnull.otyacraftengine.data.CrossDataGeneratorAccess;
import dev.felnull.otyacraftengine.data.provider.RecipeProviderWrapper;
import dev.felnull.otyacraftengine.tag.PlatformItemTags;
import net.minecraft.data.PackOutput;
import net.minecraft.data.recipes.FinishedRecipe;
import net.minecraft.data.recipes.RecipeCategory;
import net.minecraft.data.recipes.ShapedRecipeBuilder;
import net.minecraft.data.recipes.ShapelessRecipeBuilder;
import net.minecraft.tags.ItemTags;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.block.Blocks;

import java.util.function.Consumer;

public class IMPRecipeProviderWrapper extends RecipeProviderWrapper {

    public IMPRecipeProviderWrapper(PackOutput packOutput, CrossDataGeneratorAccess crossDataGeneratorAccess) {
        super(packOutput, crossDataGeneratorAccess);
    }

    @Override
    public void generateRecipe(Consumer<FinishedRecipe> exporter, RecipeProviderAccess providerAccess) {
        // Manual - A music guide crafted with knowledge and experience
        ShapedRecipeBuilder.shaped(RecipeCategory.MISC, IMPItems.MANUAL.get())
                .define('B', Items.BOOK)
                .define('C', IMPItemTags.CASSETTE_TAPE)
                .define('N', Items.NOTE_BLOCK)
                .define('Q', Items.QUILL)
                .pattern(" Q ")
                .pattern("BCB")
                .pattern(" N ")
                .unlockedBy(providerAccess.getHasName(Items.BOOK), providerAccess.has(Items.BOOK))
                .save(exporter);

        // Cassette Tape - Magnetic storage using iron and kelp tape
        ShapedRecipeBuilder.shaped(RecipeCategory.MISC, IMPItems.CASSETTE_TAPE.get())
                .define('N', PlatformItemTags.ironNuggets().getKey())
                .define('K', Items.DRIED_KELP)
                .define('R', PlatformItemTags.redstoneDusts())
                .define('S', Items.SMOOTH_STONE)
                .pattern("NKN")
                .pattern("KSK")
                .pattern("NRN")
                .unlockedBy(providerAccess.getHasName(Items.DRIED_KELP), providerAccess.has(Items.DRIED_KELP))
                .save(exporter);

        // Glass Cassette Tape - Premium version with crystal clarity
        ShapedRecipeBuilder.shaped(RecipeCategory.MISC, IMPItems.CASSETTE_TAPE_GLASS.get())
                .define('N', PlatformItemTags.ironNuggets().getKey())
                .define('K', Items.DRIED_KELP)
                .define('R', PlatformItemTags.redstoneDusts())
                .define('G', Items.GLASS)
                .define('A', Items.AMETHYST_SHARD)
                .pattern("NKN")
                .pattern("KGK")
                .pattern("ARA")
                .unlockedBy(providerAccess.getHasName(Items.AMETHYST_SHARD), providerAccess.has(Items.AMETHYST_SHARD))
                .save(exporter);

        // Radio Antenna - Reaches across dimensions for signals
        ShapedRecipeBuilder.shaped(RecipeCategory.MISC, IMPItems.RADIO_ANTENNA.get())
                .define('E', PlatformItemTags.enderPearls().getKey())
                .define('I', PlatformItemTags.ironIngots())
                .define('C', Items.COPPER_INGOT)
                .define('L', Items.LIGHTNING_ROD)
                .pattern(" E ")
                .pattern(" L ")
                .pattern("CIC")
                .group("antenna")
                .unlockedBy(providerAccess.getHasName(Items.ENDER_PEARL), providerAccess.has(PlatformItemTags.enderPearls().getKey()))
                .save(exporter);

        // Parabolic Antenna - Advanced satellite dish for premium streaming
        ShapedRecipeBuilder.shaped(RecipeCategory.MISC, IMPItems.PARABOLIC_ANTENNA.get())
                .define('E', PlatformItemTags.enderPearls().getKey())
                .define('I', PlatformItemTags.ironIngots())
                .define('B', Blocks.IRON_BLOCK)
                .define('C', Items.COPPER_INGOT)
                .define('N', Items.NETHERITE_SCRAP)
                .pattern("CEC")
                .pattern("I I")
                .pattern("NBN")
                .group("antenna")
                .unlockedBy(providerAccess.getHasName(Items.NETHERITE_SCRAP), providerAccess.has(Items.NETHERITE_SCRAP))
                .save(exporter);

        // Boombox - Portable party machine
        ShapedRecipeBuilder.shaped(RecipeCategory.MISC, IMPBlocks.BOOMBOX.get())
                .define('H', Items.TRIPWIRE_HOOK) // Handle for portability
                .define('N', Items.NOTE_BLOCK)
                .define('J', Items.JUKEBOX)
                .define('B', ItemTags.BUTTONS)
                .define('I', PlatformItemTags.ironIngots())
                .define('R', PlatformItemTags.redstoneDusts())
                .pattern("HBH")
                .pattern("NJN")
                .pattern("IRI")
                .unlockedBy(providerAccess.getHasName(Items.JUKEBOX), providerAccess.has(Items.JUKEBOX))
                .save(exporter);

        // Cassette Deck - Professional recording equipment
        ShapedRecipeBuilder.shaped(RecipeCategory.MISC, IMPBlocks.CASSETTE_DECK.get())
                .define('R', PlatformItemTags.redstoneDusts())
                .define('I', PlatformItemTags.ironIngots())
                .define('N', Items.NOTE_BLOCK)
                .define('J', Items.JUKEBOX)
                .define('O', Items.OBSERVER) // For precise timing
                .define('C', Items.COMPARATOR) // For signal processing
                .pattern("IRI")
                .pattern("NJN")
                .pattern("OCO")
                .unlockedBy(providerAccess.getHasName(Items.JUKEBOX), providerAccess.has(Items.JUKEBOX))
                .save(exporter);

        // Music Manager - Advanced digital music system
        ShapedRecipeBuilder.shaped(RecipeCategory.MISC, IMPBlocks.MUSIC_MANAGER.get())
                .define('D', PlatformItemTags.diamonds())
                .define('I', PlatformItemTags.ironIngots())
                .define('G', Items.TINTED_GLASS) // Premium display
                .define('R', Items.REDSTONE_BLOCK) // Powerful processing
                .define('E', Items.ENDER_EYE) // Network connectivity
                .define('N', Items.NETHERITE_INGOT) // High-end materials
                .pattern("DGD")
                .pattern("ERE")
                .pattern("INI")
                .unlockedBy(providerAccess.getHasName(Items.NETHERITE_INGOT), providerAccess.has(Items.NETHERITE_INGOT))
                .save(exporter);
    }
}
