architectury {
    common(rootProject.enabled_platforms.split(","))
}

loom {
    accessWidenerPath = file("src/main/resources/iammusicplayer.accesswidener")
}

dependencies {
    modImplementation "net.fabricmc:fabric-loader:${rootProject.fabric_loader_version}"

    modApi "dev.architectury:architectury:${rootProject.architectury_version}"

    modApi "dev.felnull:otyacraftengine:${rootProject.oe_version}"
    modApi "me.shedaniel.cloth:cloth-config:${rootProject.cloth_config_version}"

    implementation "dev.felnull:felnull-java-library:${rootProject.felnull_version}"

    /*implementation("com.github.walkyst:lavaplayer-fork:${rootProject.lava_version}") {
        exclude group: 'com.sedmelluq', module: 'lavaplayer-natives'
    }*/
    implementation("dev.arbjerg:lavaplayer:${rootProject.lava_version_youtube}") {
        exclude group: 'dev.arbjerg', module: 'lavaplayer-natives'
    }
    implementation("dev.lavalink.youtube:v2:${rootProject.lavalink}")

    implementation "com.github.sealedtx:java-youtube-downloader:${rootProject.ytdownloader}"
    implementation 'com.mpatric:mp3agic:0.9.1'
}
/*
publishing {
    publications {
        mavenCommon(MavenPublication) {
            artifactId = rootProject.archives_base_name
            from components.java
            pom {
                name = 'IamMusicPlayer'
                description = 'The ikisugi music player mod.'
                licenses {
                    license {
                        name = 'GNU Lesser General Public License v3.0'
                        url = 'https://www.gnu.org/licenses/lgpl-3.0.txt'
                    }
                }
                developers {
                    developer {
                        id = 'MORIMORI0317'
                        name = 'MORIMORI0317'
                    }
                    developer {
                        id = 'FelNull'
                        name = 'TeamFelNull'
                        email = '<EMAIL>'
                    }
                }
            }
        }
    }

    repositories {
        maven {
            url "${project.maven_put_url}"
            credentials {
                username = "felnull"
                password = "${project.maven_put_pass}" != '' ? "${project.maven_put_pass}" : System.getenv('mavenpassword')
            }
        }
    }
}
*/
